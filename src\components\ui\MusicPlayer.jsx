import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const MusicPlayerContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 20px;
  border: 1px solid ${({ theme }) => theme.border};
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 8px ${({ theme }) => theme.shadow};
  }
`;

const PlayButton = styled(motion.button)`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    45deg,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  color: white;
  font-size: 10px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
`;

const MusicInfo = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const MusicTitle = styled.span`
  font-size: 0.75rem;
  font-weight: 500;
  color: ${({ theme }) => theme.text};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
`;

const MusicStatus = styled.span`
  font-size: 0.6rem;
  color: ${({ theme }) => theme.textSecondary};
  opacity: 0.8;
`;

const ProgressBar = styled.div`
  width: 60px;
  height: 2px;
  background-color: ${({ theme }) => theme.border};
  border-radius: 1px;
  overflow: hidden;
  margin-top: 2px;
`;

const Progress = styled(motion.div)`
  height: 100%;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  border-radius: 1px;
`;

const MusicPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [userInteracted, setUserInteracted] = useState(false);
  const audioRef = useRef(null);

  const musicTitle = "Purple Dream";
  const musicPath = "/music/Ghostrifter-Purple_Dream.mp3";

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      if (audio.duration) {
        const progressPercent = (audio.currentTime / audio.duration) * 100;
        setProgress(progressPercent);
        setCurrentTime(audio.currentTime);
      }
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(0);
      setCurrentTime(0);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  // 监听用户交互，首次交互后自动播放
  useEffect(() => {
    const handleUserInteraction = async () => {
      if (!userInteracted) {
        setUserInteracted(true);
        // 延迟一下再自动播放，让用户看到音乐播放器
        setTimeout(async () => {
          const audio = audioRef.current;
          if (audio) {
            try {
              await audio.play();
              setIsPlaying(true);
            } catch (error) {
              console.log('自动播放失败，用户需要手动点击播放');
            }
          }
        }, 500);
      }
    };

    // 监听多种用户交互事件
    const events = ['click', 'keydown', 'scroll', 'touchstart'];

    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [userInteracted]);

  const handlePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('音频播放失败:', error);
    }
  };



  return (
    <>
      <audio
        ref={audioRef}
        src={musicPath}
        preload="metadata"
        loop
      />
      
      <MusicPlayerContainer>
        <PlayButton
          onClick={handlePlay}
          whileTap={{ scale: 0.95 }}
          animate={{ 
            rotate: isPlaying ? 360 : 0,
          }}
          transition={{ 
            rotate: { 
              duration: isPlaying ? 2 : 0.3, 
              repeat: isPlaying ? Infinity : 0,
              ease: "linear"
            }
          }}
        >
          {isPlaying ? '⏸' : '▶'}
        </PlayButton>
        
        <MusicInfo>
          <MusicTitle>{musicTitle}</MusicTitle>
          <ProgressBar>
            <Progress
              initial={{ width: '0%' }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </ProgressBar>
        </MusicInfo>
      </MusicPlayerContainer>
    </>
  );
};

export default MusicPlayer;
