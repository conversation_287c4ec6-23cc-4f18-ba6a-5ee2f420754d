import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const MusicPlayerContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: ${({ theme }) => theme.backgroundAlt};
  border-radius: 20px;
  border: 1px solid ${({ theme }) => theme.border};
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 8px ${({ theme }) => theme.shadow};
  }
`;

const PlayButton = styled(motion.button)`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    45deg,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  color: white;
  font-size: 10px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
`;

const MusicInfo = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const MusicTitle = styled.span`
  font-size: 0.75rem;
  font-weight: 500;
  color: ${({ theme }) => theme.text};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
`;

const MusicStatus = styled.span`
  font-size: 0.6rem;
  color: ${({ theme }) => theme.textSecondary};
  opacity: 0.8;
`;

const ProgressBar = styled.div`
  width: 60px;
  height: 2px;
  background-color: ${({ theme }) => theme.border};
  border-radius: 1px;
  overflow: hidden;
  margin-top: 2px;
`;

const Progress = styled(motion.div)`
  height: 100%;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  border-radius: 1px;
`;

const MusicPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [userInteracted, setUserInteracted] = useState(false);
  const audioRef = useRef(null);

  const musicTitle = "Purple Dream";
  const musicPath = "/music/Ghostrifter-Purple_Dream.mp3";

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      if (audio.duration) {
        const progressPercent = (audio.currentTime / audio.duration) * 100;
        setProgress(progressPercent);
        setCurrentTime(audio.currentTime);
      }
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(0);
      setCurrentTime(0);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  // 多种策略实现自动播放
  useEffect(() => {
    const tryAutoPlay = async () => {
      const audio = audioRef.current;
      if (!audio || userInteracted) return;

      try {
        // 策略1: 直接尝试播放
        await audio.play();
        setIsPlaying(true);
        setUserInteracted(true);
        console.log('自动播放成功');
        return;
      } catch (error) {
        console.log('直接自动播放失败，尝试其他策略');
      }

      // 策略2: 设置静音后播放，然后逐渐恢复音量
      try {
        audio.muted = true;
        await audio.play();
        setIsPlaying(true);

        // 逐渐恢复音量
        let volume = 0;
        audio.volume = 0;
        audio.muted = false;

        const fadeIn = setInterval(() => {
          volume += 0.1;
          if (volume >= 1) {
            volume = 1;
            clearInterval(fadeIn);
          }
          audio.volume = volume;
        }, 100);

        setUserInteracted(true);
        console.log('静音自动播放成功，音量已恢复');
        return;
      } catch (error) {
        console.log('静音播放也失败，等待用户交互');
      }
    };

    // 页面加载后立即尝试
    const timer1 = setTimeout(tryAutoPlay, 100);

    // 如果失败，再次尝试
    const timer2 = setTimeout(tryAutoPlay, 1000);

    // 监听用户交互事件 - 必须在事件处理器内直接调用play()
    const handleUserInteraction = (event) => {
      if (!userInteracted) {
        setUserInteracted(true);
        const audio = audioRef.current;
        if (audio && !isPlaying) {
          // 必须在用户事件处理器内同步调用play()
          audio.muted = false;
          audio.volume = 1;
          audio.play().then(() => {
            setIsPlaying(true);
            console.log('用户交互后播放成功');
          }).catch(error => {
            console.log('用户交互后播放失败:', error);
          });
        }
      }
    };

    // 只监听真正的用户交互事件，不包括scroll和mousemove
    const events = ['click', 'keydown', 'touchstart'];

    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true, passive: false });
    });

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [userInteracted, isPlaying]);

  const handlePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('音频播放失败:', error);
    }
  };



  return (
    <>
      <audio
        ref={audioRef}
        src={musicPath}
        preload="auto"
        loop
        playsInline
        crossOrigin="anonymous"
      />

      {/* 不可见的自动播放触发器 */}
      {!userInteracted && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 9999,
            background: 'transparent',
            cursor: 'pointer'
          }}
          onClick={async (e) => {
            e.stopPropagation();
            const audio = audioRef.current;
            if (audio && !userInteracted) {
              setUserInteracted(true);
              try {
                audio.muted = false;
                audio.volume = 1;
                await audio.play();
                setIsPlaying(true);
                console.log('点击触发播放成功');
              } catch (error) {
                console.log('点击触发播放失败:', error);
              }
            }
          }}
          title="点击任意位置开始播放音乐"
        />
      )}

      <MusicPlayerContainer>
        <PlayButton
          onClick={handlePlay}
          whileTap={{ scale: 0.95 }}
        >
          {isPlaying ? '⏸' : '▶'}
        </PlayButton>

        <MusicInfo>
          <MusicTitle>{musicTitle}</MusicTitle>
          <ProgressBar>
            <Progress
              initial={{ width: '0%' }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </ProgressBar>
        </MusicInfo>
      </MusicPlayerContainer>
    </>
  );
};

export default MusicPlayer;
