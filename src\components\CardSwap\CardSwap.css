.card-swap-container {
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translate(5%, 20%);
  transform-origin: bottom right;
  perspective: 900px;
  overflow: visible;
}

.card {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 15px;
  background: #ffffff;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.07);
  transform-style: preserve-3d;
  will-change: transform;
  backface-visibility: hidden;
  overflow: hidden;
}

@media (max-width: 768px) {
  .card-swap-container {
    position: relative;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translateX(-50%) scale(0.75);
    transform-origin: center center;
  }
}

@media (max-width: 480px) {
  .card-swap-container {
    position: relative;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translateX(-50%) scale(0.6);
    transform-origin: center center;
  }
}

@media (max-width: 360px) {
  .card-swap-container {
    position: relative;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translateX(-50%) scale(0.5);
    transform-origin: center center;
  }
}